{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.void_hydra_right_head.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false || q.all_animations_finished"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "idling": {"animations": ["idle"], "transitions": [{"right_atomic": "q.property('ptd_dbb:attack') == 'right_atomic' && q.property('ptd_dbb:cooling_down') == false"}, {"dead": "q.property('ptd_dbb:dead') == true && q.property('ptd_dbb:cooling_down') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "right_atomic": {"animations": ["right_atomic"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "dead": {"animations": ["death"]}}}}}