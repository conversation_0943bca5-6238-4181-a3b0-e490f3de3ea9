{
  "format_version": "1.21.80",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_dbb:void_hydra",
      "is_spawnable": true,
      "is_summonable": true,
      "properties": {
        "ptd_dbb:spawning": {
          "type": "bool",
          "client_sync": true,
          "default": true
        },
        "ptd_dbb:dead": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_dbb:attack": {
          "type": "enum",
          "client_sync": true,
          "default": "none",
          "values": [
            "none",
            "right_atomic_cross",
            "right_vacuum",
            "right_summon",
            "mid_atomic",
            "mid_meteor",
            "mid_singularity",
            "left_atomic_cross",
            "left_atomic",
            "left_railgun",
            "left_missile",
            "left_shout"
          ]
        },
        "ptd_dbb:cooling_down": {
          "type": "bool",
          "client_sync": true,
          "default": false
        }
      }
    },
    "component_groups": {
      "ptd_dbb:spawning": {
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 14,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:on_spawn",
            "target": "self"
          }
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "cause": "all",
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:default": {
        "minecraft:behavior.move_towards_home_restriction": {
          "priority": 7
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 10
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_dbb:dead",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_dbb:spawning",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "is_family",
                  "subject": "other",
                  "value": "void_hydra"
                }
              },
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:targeting": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 0,
          "must_see": true,
          "attack_interval": 1,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "reevaluate_description": true,
          "entity_types": [
            {
              "priority": 0,
              "max_dist": 128,
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "player"
              }
            },
            {
              "priority": 1,
              "max_dist": 128,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "boss"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "void_hydra"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            },
            {
              "priority": 2,
              "max_dist": 128,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "minion"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "void_hydra"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            }
          ]
        }
      },
      "ptd_dbb:melee": {
        "minecraft:movement": {
          "value": 0.015
        },
        "minecraft:movement.basic": {},
        "minecraft:navigation.float": {
          "can_path_over_water": true,
          "can_sink": false,
          "can_pass_doors": false,
          "can_path_from_air": true,
          "avoid_water": true,
          "avoid_damage_blocks": true,
          "avoid_sun": false
        },
        "minecraft:jump.static": {},
        "minecraft:behavior.float": {
          "priority": 0
        },
        "minecraft:behavior.random_look_around": {
          "priority": 10
        },
        "minecraft:behavior.float_wander": {
          "priority": 8,
          "must_reach": true,
          "navigate_around_surface": true,
          "additional_collision_buffer": true,
          "surface_xz_dist": 12,
          "xz_dist": 16,
          "y_dist": 12,
          "y_offset": -1,
          "surface_y_dist": 24
        }
      },
      "ptd_dbb:dead": {
        "minecraft:timer": {
          "time": 20,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:despawn",
            "target": "self"
          }
        },
        "minecraft:is_collidable": {},
        "minecraft:movement": {
          "value": 0
        },
        "minecraft:navigation.walk": {
          "is_amphibious": false,
          "can_pass_doors": false,
          "can_walk": false,
          "can_swim": false,
          "can_sink": false,
          "avoid_sun": false
        },
        "minecraft:behavior.random_look_around": {
          "priority": 999999
        },
        "minecraft:behavior.random_stroll": {
          "priority": 999999
        },
        "minecraft:body_rotation_blocked": {}
      },
      "ptd_dbb:despawn": {
        "minecraft:instant_despawn": {}
      },
      "ptd_dbb:spawn_right_head": {
        "minecraft:addrider": {
          "entity_type": "ptd_dbb:void_hydra_right_head"
        }
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "sequence": [
          {
            "queue_command": {
              "command": "tp @s ~ ~8 ~"
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:spawning", "ptd_dbb:spawn_right_head"]
            }
          }
        ]
      },
      "ptd_dbb:on_spawn": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:spawning", "ptd_dbb:spawn_right_head"]
            },
            "set_property": {
              "ptd_dbb:spawning": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]
            }
          }
        ]
      },
      "ptd_dbb:dead": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:dead": true
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_dbb:targeting", "ptd_dbb:melee"]
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:dead", "ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:despawn": {
        "add": {
          "component_groups": ["ptd_dbb:despawn"]
        }
      },
      "ptd_dbb:on_load": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none",
              "ptd_dbb:cooling_down": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]
            }
          }
        ]
      }
    },
    "components": {
      "minecraft:type_family": {
        "family": ["void_hydra", "boss"]
      },
      "minecraft:collision_box": {
        "width": 4,
        "height": 6
      },
      "minecraft:custom_hit_test": {
        "hitboxes": [
          {
            "width": 13.5,
            "height": 10.7,
            "pivot": [0, 5.35, 0]
          }
        ]
      },
      "minecraft:health": {
        "value": 1500,
        "max": 1500
      },
      "minecraft:boss": {
        "hud_range": 128,
        "name": "Void Hydra",
        "should_darken_sky": false
      },
      "minecraft:can_fly": {},
      "minecraft:navigation.float": {},
      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:knockback_resistance": {
        "value": 1
      },
      "minecraft:follow_range": {
        "value": 256,
        "max": 256
      },
      "minecraft:home": {
        "restriction_radius": 32,
        "restriction_type": "random_movement"
      },
      "minecraft:rideable": {
        "seat_count": 3,
        "family_types": ["void_hydra_head"],
        "pull_in_entities": true,
        "passenger_max_width": 0,
        "seats": [
          // Right head
          {
            "position": [-3.4, 3.2, 4.1],
            "lock_rider_rotation": 45
          },
          // Middle head
          {
            "position": [0, 3.2, 4.1],
            "lock_rider_rotation": 45
          },
          // Left head
          {
            "position": [3.4, 3.2, 4.1],
            "lock_rider_rotation": 45
          }
        ]
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "test": "actor_health",
                "subject": "self",
                "operator": "<=",
                "value": 10
              },
              "event": "ptd_dbb:dead",
              "target": "self"
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:dead",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "void_hydra"
              }
            },
            "deals_damage": "no"
          }
        ]
      },
      "minecraft:variable_max_auto_step": {
        "base_value": 1.0625,
        "jump_prevented_value": 1.0625
      },
      "minecraft:physics": {},
      "minecraft:floats_in_liquid": {},
      "minecraft:persistent": {},
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
