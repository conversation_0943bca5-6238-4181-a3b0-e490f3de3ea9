{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.void_hydra.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false || q.all_animations_finished"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "idling": {"animations": ["idle"], "transitions": [{"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "dead": {"animations": ["death"]}}}}}