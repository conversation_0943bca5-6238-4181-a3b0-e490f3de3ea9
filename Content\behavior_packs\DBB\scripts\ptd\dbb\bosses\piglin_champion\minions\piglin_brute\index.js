import { executeVerticalAttack } from "./attacks/vertical";
import { executeHorizontalAttack } from "./attacks/horizontal";
/**
 * Animation times for different attacks
 */
const VERTICAL_ANIMATION_TIME = 13; // 0.6667 seconds
const HORIZONTAL_ANIMATION_TIME = 15; // 0.75 seconds
/**
 * Handles all mechanics for the Piglin Brute
 * This includes attack selection, attack execution, and cooldown management
 * Similar to zombie brute but with two different attack types
 *
 * @param piglinBrute The piglin brute entity
 */
export function piglinBruteMechanics(piglinBrute) {
    // Skip if entity is not valid
    try {
        if (!piglinBrute)
            return;
        // Skip if entity is spawning or dead
        const isSpawning = piglinBrute.getProperty("ptd_dbb:spawning");
        const isDead = piglinBrute.getProperty("ptd_dbb:dead");
        if (isSpawning || isDead)
            return;
        // Get the current attack type
        const attackType = piglinBrute.getProperty("ptd_dbb:attack");
        // Apply slowness effect to prevent movement during the attack
        // Use different durations based on attack type
        if (attackType === "vertical") {
            piglinBrute.addEffect("minecraft:slowness", VERTICAL_ANIMATION_TIME, { amplifier: 250, showParticles: false });
            executeVerticalAttack(piglinBrute);
        }
        else if (attackType === "horizontal") {
            piglinBrute.addEffect("minecraft:slowness", HORIZONTAL_ANIMATION_TIME, { amplifier: 250, showParticles: false });
            executeHorizontalAttack(piglinBrute);
        }
    }
    catch (e) {
        return;
    }
    return;
}
