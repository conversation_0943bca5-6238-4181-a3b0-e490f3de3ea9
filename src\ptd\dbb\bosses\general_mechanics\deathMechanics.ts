import { Dimension, Entity, ItemStack, Vector3, system } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";

/**
 * Configuration options for boss death mechanics
 */
export interface DeathMechanicsOptions {
  /**
   * Total duration of death animation in ticks
   * @default 100
   */
  duration?: number;

  /**
   * XP orb spawn configuration
   */
  xpOrbs?: {
    /**
     * Number of XP orbs to spawn per tick
     * @default 4
     */
    count: number;

    /**
     * Duration in ticks to spawn XP orbs
     * @default 50
     */
    duration: number;

    /**
     * Height offset for spawning XP orbs
     * @default 2.25
     */
    heightOffset?: number;
  };

  /**
   * Item drops configuration
   * Can be either an array of item descriptors (legacy format) or an array of ItemStacks
   */
  drops?: Array<
    | {
        /**
         * Item identifier to drop
         */
        item: string;

        /**
         * Number of items to drop
         * Can be a fixed number or a [min, max] range for random amount
         */
        count: number | [number, number];

        /**
         * Height offset for dropping items
         * @default 2.25
         */
        heightOffset?: number;
      }
    | {
        /**
         * Pre-configured ItemStack to drop
         */
        itemStack: ItemStack;

        /**
         * Height offset for dropping items
         * @default 2.25
         */
        heightOffset?: number;
      }
  >;

  /**
   * Sound to play when death sequence starts
   */
  deathSound?: string;

  /**
   * Callback function when death sequence starts (tick 1)
   */
  onStart?: (entity: Entity) => void;

  /**
   * Callback function when death sequence completes (final tick)
   * Default behavior is to remove the entity
   */
  onComplete?: (entity: Entity) => void;

  /**
   * Custom events at specific ticks during death sequence
   */
  customEvents?: Array<{
    /**
     * Tick number when the event should trigger
     */
    tick: number;

    /**
     * Callback function to execute
     */
    callback: (entity: Entity) => void;
  }>;

  /**
   * Property prefix for entity properties
   * @default "ptd_dbb"
   */
  propertyPrefix?: string;

  /**
   * Function to stop boss sounds
   * Each boss must provide its own sound stopping function
   */
  stopSoundsFn?: (entity: Entity, excludedSound?: string) => void;
}

/**
 * Default options for death mechanics
 */
const DEFAULT_OPTIONS = {
  duration: 100,
  xpOrbs: {
    count: 4,
    duration: 50,
    heightOffset: 2.25
  },
  propertyPrefix: "ptd_dbb"
  // Note: stopSoundsFn is required and must be provided by each boss implementation
  // It is not included in the default options
};

/**
 * Handles generalized boss death mechanics
 *
 * @param entity The boss entity
 * @param options Configuration options for the death sequence (stopSoundsFn is required)
 * @param currentTick The current tick number in the death sequence (1-based)
 * @returns true if the entity is dead and death mechanics were applied, false otherwise
 */
export function handleDeathMechanics(entity: Entity, options: DeathMechanicsOptions, currentTick: number, teleport: boolean = true): boolean {
  // Skip if entity is not valid
  try {
    if (!entity) return false;
  } catch (e) {
    return false;
  }

  // Merge default options with provided options
  const config = { ...DEFAULT_OPTIONS, ...options };
  const prefix = config.propertyPrefix || "ptd_dbb";

  // Get entity location
  const entityLocation = entity.location;

  // Define the dynamic property name for death location
  const DEATH_LOCATION_PROPERTY = `${prefix}:death_location`;

  // Apply slowness effect to prevent movement
  entity.addEffect("minecraft:slowness", 20, { amplifier: 255, showParticles: false });

  // Save the death location when the death sequence begins (currentTick === 1)
  if (currentTick === 1) {
    const deathLocation = JSON.stringify({
      x: entityLocation.x,
      y: entityLocation.y,
      z: entityLocation.z
    });
    entity.setDynamicProperty(DEATH_LOCATION_PROPERTY, deathLocation);

    // Execute onStart callback if provided
    if (config.onStart) {
      config.onStart(entity);
    }

    // Play death sound if provided
    if (config.deathSound) {
      // Stop all other sound effects except the death sound
      config.stopSoundsFn?.(entity, config.deathSound);

      // Play the death sound
      entity.dimension.playSound(config.deathSound, entityLocation, { volume: 8 });
    }

    // Spawn configured item drops
    if (config.drops && config.drops.length > 0) {
      for (const drop of config.drops) {
        const heightOffset = drop.heightOffset !== undefined ? drop.heightOffset : 2.25;
        const spawnLocation = {
          x: entityLocation.x,
          y: entityLocation.y + heightOffset,
          z: entityLocation.z
        };

        // Check if this is a pre-configured ItemStack or a legacy format drop
        if ("itemStack" in drop) {
          // This is a pre-configured ItemStack
          entity.dimension.spawnItem(drop.itemStack, spawnLocation);
        } else if ("item" in drop) {
          // This is a legacy format drop with item and count
          // Calculate item count (fixed or random range)
          let itemCount: number;
          if (Array.isArray(drop.count)) {
            const [min, max] = drop.count;
            itemCount = Math.floor(Math.random() * (max - min + 1)) + min;
          } else {
            itemCount = drop.count;
          }

          // Spawn the items
          if (itemCount > 0) {
            entity.dimension.spawnItem(new ItemStack(drop.item, itemCount), spawnLocation);
          }
        }
      }
    }
  }

  // Spawn XP orbs during the configured duration
  if (config.xpOrbs && currentTick <= (config.xpOrbs.duration || 50)) {
    const xpCount = config.xpOrbs.count || 4;
    const heightOffset = config.xpOrbs.heightOffset !== undefined ? config.xpOrbs.heightOffset : 2.25;

    // Spawn the configured number of XP orbs per tick
    for (let i = 0; i < xpCount; i++) {
      entity.dimension.spawnEntity("minecraft:xp_orb", {
        x: entityLocation.x,
        y: entityLocation.y + heightOffset,
        z: entityLocation.z
      });
    }
  }

  // Teleport to the saved death location for the entire death animation
  const savedLocationJson = entity.getDynamicProperty(DEATH_LOCATION_PROPERTY) as string;
  if (savedLocationJson && teleport) {
    try {
      const savedLocation = JSON.parse(savedLocationJson) as Vector3;
      // Teleport to the saved location
      entity.teleport(savedLocation);
    } catch (e) {
      // Fallback to current behavior if JSON parsing fails
      entity.teleport(entityLocation);
    }
  }

  // Execute custom events at specific ticks
  if (config.customEvents) {
    for (const event of config.customEvents) {
      if (currentTick === event.tick) {
        event.callback(entity);
      }
    }
  }

  // Handle completion of death animation
  if (currentTick === (config.duration || 100)) {
    if (config.onComplete) {
      config.onComplete(entity);
    } else {
      // Default behavior: remove the entity
      entity.remove();
    }
  }

  return true;
}

/**
 * Applies camera shake to players within a radius of the entity over a specified interval
 * @param source The entity causing the camera shake
 * @param radius The radius in blocks to apply the camera shake
 * @param intervalLength The duration in ticks to continue applying the camera shake
 * @param shakeLength The length of each camera shake effect
 * @param minStrength The minimum strength of the camera shake
 * @param maxStrength The maximum strength of the camera shake
 */
export function applyDeathCameraShake(
  source: Entity,
  intervalLength: number,
  radius: number = 32,
  shakeLength: number = 0.05,
  minStrength: number = 0.02,
  maxStrength: number = 0.075
): void {
  const sourceLocation: Vector3 = source.location;
  const dimension: Dimension = source.dimension;

  // Initialize tick counter
  let ticksElapsed = 0;

  // Set up interval to apply camera shake every tick for the intervalLength duration
  const intervalId = system.runInterval(() => {
    try {
      // Get all players within the radius
      const players = dimension.getPlayers({
        location: sourceLocation,
        maxDistance: radius
      });

      // Apply camera shake to each player
      players.forEach((player) => {
        // Only apply to players on the ground
        if (!player.isOnGround) return;

        // Calculate distance from source
        const distance = getDistance(sourceLocation, player.location);

        // Skip if outside radius (should be handled by getPlayers, but just in case)
        if (distance > radius) return;

        // Calculate strength based on distance (inversely proportional)
        // Players closer to the source get stronger shake
        const strengthMultiplier = 1 - distance / radius; // 0 at edge, 1 at center
        const strength = minStrength + strengthMultiplier * (maxStrength - minStrength);

        // Apply camera shake using /camerashake command with player's name
        player.runCommand(`camerashake add @s ${strength.toFixed(2)} ${shakeLength.toFixed(2)} positional`);
      });

      // Increment the tick counter
      ticksElapsed++;

      // Clear the interval once we've reached the specified duration
      if (ticksElapsed >= intervalLength) {
        system.clearRun(intervalId);
      }
    } catch (error) {
      // If any error occurs (likely because source entity no longer exists)
      system.clearRun(intervalId);
    }
  }, 1); // Run every tick
}
