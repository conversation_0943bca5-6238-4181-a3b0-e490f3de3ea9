{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Peach Tree Designs LLC", "name": "Dr<PERSON>'s Bosses", "packs": {"behaviorPack": "./Content/behavior_packs/DBB", "resourcePack": "./Content/resource_packs/DBB"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {"addon_builder": {"url": "github.com/Raboy-13/Regolith-Filters", "version": "a0793cb99c778f38e8b13f95b34e2bc7b4ad17d4"}, "texture_list": {"url": "github.com/Bedrock-OSS/regolith-filters", "version": "1.1.2"}}, "formatVersion": "1.4.0", "profiles": {"default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}, "build": {"export": {"readOnly": false, "target": "local"}, "filters": []}}}}