{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:zombie_brute", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest", "circle": "entity_emissive_alpha", "light": "emissive_translucent"}, "textures": {"default": "textures/ptd/dbb/entity/bosses/necromancer/minions/zombie_brute"}, "geometry": {"default": "geometry.ptd_dbb_zombie_brute"}, "animations": {"look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_dbb.zombie_brute.spawn", "idle": "animation.ptd_dbb.zombie_brute.idle", "walk": "animation.ptd_dbb.zombie_brute.walk", "attack": "animation.ptd_dbb.zombie_brute.attack", "death": "animation.ptd_dbb.zombie_brute.death", "no_effects": "animation.ptd_dbb.zombie_brute.no_effects", "general": "controller.animation.ptd_dbb.zombie_brute.general"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", {"look_at_target": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}, {"no_effects": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}]}, "render_controllers": ["controller.render.ptd_dbb.zombie_brute"], "spawn_egg": {"base_color": "#8c5032", "overlay_color": "#584036"}}}}