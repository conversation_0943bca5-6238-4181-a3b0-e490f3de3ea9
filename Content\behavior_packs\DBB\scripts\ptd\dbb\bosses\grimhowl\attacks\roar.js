/*
* Grimhowl Roar Attack
* This function handles the roar attack of the Grimhowl boss.
* It triggers a roar animation, plays a sound, and applies knockback and effects to nearby entities.
*/
import { system, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getDirection } from "../../../utilities/vector3";
/**
 * Handles the Grimhowl roar attack.
 * @param {Entity} sourceEntity - The entity performing the roar attack.
 */
export function doGrimhowlRoar(sourceEntity) {
    try {
        if (!sourceEntity)
            return;
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_roar');
        sourceEntity.teleport(sourceEntity.location, { keepVelocity: false }); // Stand still for the roar animation
        sourceEntity.applyImpulse({ x: 0, y: -1, z: 0 });
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.roar @a ~ ~ ~ 32 ${1 + (Math.random() * 0.3)}`);
            const grimHowlLocation = sourceEntity.location;
            const nearbyEntities = sourceEntity.dimension.getEntities({
                location: grimHowlLocation,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 16
            }).filter(filterValidTargets(sourceEntity));
            nearbyEntities.forEach((entity) => {
                // Calculate the CURRENT direction vector from the sourceEntity to the entity
                const direction = getDirection(sourceEntity.location, entity.location);
                // Normalize the direction vector
                const magnitude = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
                const normalizedDirection = magnitude === 0
                    ? { x: 0, y: 0, z: 0 }
                    : {
                        x: direction.x / magnitude,
                        y: direction.y / magnitude,
                        z: direction.z / magnitude
                    };
                // Calculate the location opposite to the sourceEntity
                const oppositeLocation = {
                    x: entity.location.x + normalizedDirection.x * 2, // Move 2 blocks in the opposite direction
                    y: entity.location.y,
                    z: entity.location.z + normalizedDirection.z * 2
                };
                // Teleport the entity to its current location and make it face the opposite direction
                entity.teleport(entity.location, { facingLocation: oppositeLocation });
                // Apply knockback to the entity
                entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 5, 1); // Adjust knockback strength as needed
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.roar.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.magic }); // Apply damage
                entity.addEffect('minecraft:blindness', 20, { amplifier: 255, showParticles: false });
                entity.addEffect('minecraft:weakness', 30, { amplifier: 255, showParticles: false });
                entity.runCommand(`camerashake add @s 0.1 1 rotational`);
            });
        }, 0.75 * 20);
    }
    catch (error) {
        sourceEntity?.triggerEvent('ptd_dbb:attack_done');
    }
}
