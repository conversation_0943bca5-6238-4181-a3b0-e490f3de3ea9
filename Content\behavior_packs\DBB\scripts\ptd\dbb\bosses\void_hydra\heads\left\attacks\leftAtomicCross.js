import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../../general_attacks/attackDamages";
import { fixedLenRaycast } from "../../../../../utilities/raycasts";
const DAMAGE_TIMING = 60;
const ANIMATION_TIME = 120;
const COOLDOWN_TIME = 20;
const ATTACK_CONFIG = {
    RANGE: 16,
    BEAM_WIDTH: 3,
};
export function executeLeftAtomicCrossAttack(voidHydra) {
    let damageTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(damageTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "left_atomic_cross") {
                performAtomicCrossDamage(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(damageTiming);
        }
    }, DAMAGE_TIMING);
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "left_atomic_cross") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
function performAtomicCrossDamage(voidHydra) {
    try {
        const origin = voidHydra.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.left_atomic_cross.damage;
        const crossDirections = [
            { x: 1, y: 0, z: 0 }, { x: -1, y: 0, z: 0 }, { x: 0, y: 0, z: 1 }, { x: 0, y: 0, z: -1 }
        ];
        crossDirections.forEach(direction => {
            // Use raycast utility to get beam positions
            const beamPositions = fixedLenRaycast(origin, direction, ATTACK_CONFIG.RANGE, 0.5);
            beamPositions.forEach((centerPos, index) => {
                // Create beam width by checking positions around the center
                for (let width = -ATTACK_CONFIG.BEAM_WIDTH; width <= ATTACK_CONFIG.BEAM_WIDTH; width++) {
                    let beamPos;
                    if (direction.x !== 0) {
                        // Horizontal beam (vary Z position for width)
                        beamPos = {
                            x: centerPos.x,
                            y: centerPos.y,
                            z: centerPos.z + width
                        };
                    }
                    else {
                        // Vertical beam (vary X position for width)
                        beamPos = {
                            x: centerPos.x + width,
                            y: centerPos.y,
                            z: centerPos.z
                        };
                    }
                    voidHydra.dimension
                        .getEntities({
                        location: beamPos,
                        maxDistance: 1.5,
                        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
                        excludeFamilies: ["void_hydra", "boss"]
                    })
                        .forEach((entity) => {
                        entity.applyDamage(damage, {
                            cause: EntityDamageCause.entityAttack,
                            damagingEntity: voidHydra
                        });
                    });
                    // Spawn particles for visual effect (reduce density)
                    if (index % 4 === 0 && width === 0) { // Only spawn on center line and reduce frequency
                        voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", beamPos);
                    }
                }
            });
        });
        voidHydra.dimension.spawnParticle("minecraft:large_explosion", origin);
    }
    catch (error) {
        // Handle errors silently
    }
}
