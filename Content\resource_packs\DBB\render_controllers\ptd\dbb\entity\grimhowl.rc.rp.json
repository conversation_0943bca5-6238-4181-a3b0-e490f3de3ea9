{"format_version": "1.8.0", "render_controllers": {"controller.render.ptd_dbb.grimhowl_effects": {"geometry": "Geometry.effects", "materials": [{"*": "Material.effects"}], "textures": ["Texture.default"], "ignore_lighting": true, "part_visibility": [{"vfx_sword_swing": "(query.property('ptd_dbb:move_list') == 'grimhowl_backstep' || query.property('ptd_dbb:move_list') == 'grimhowl_slash' || query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash' || query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught') && query.property('ptd_dbb:sword_mode')"}, {"vfx_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"vfx_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"vfx_crack": "0.0"}, {"vfx_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"vfx_roar_1": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"vfx_roar_2": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"vfx_roar_3": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"effect": "0.0"}], "is_hurt_color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 0.0}}, "controller.render.ptd_dbb.grimhowl": {"geometry": "Geometry.default", "materials": [{"*": "query.property('ptd_dbb:death_timer') > 90 ? Material.effects : Material.default"}], "textures": ["query.property('ptd_dbb:enraged') ? Texture.enraged : Texture.default"], "part_visibility": [{"vfx_sword_swing": "query.property('ptd_dbb:move_list') == 'grimhowl_backstep' || query.property('ptd_dbb:move_list') == 'grimhowl_slash' || query.property('ptd_dbb:move_list') == 'grimhowl_spinning_slash' || query.property('ptd_dbb:move_list') == 'grimhowl_shadow_onslaught' && query.property('ptd_dbb:sword_mode')"}, {"vfx_claw_left": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_left'"}, {"vfx_claw_right": "query.property('ptd_dbb:move_list') == 'grimhowl_claw_right'"}, {"sword": "query.property('ptd_dbb:sword_mode') || query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"blade": "query.property('ptd_dbb:sword_mode') || query.property('ptd_dbb:move_list') == 'transition_to_sword'"}, {"vfx_crack": "0.0"}, {"vfx_roar": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"vfx_roar_1": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"vfx_roar_2": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"vfx_roar_3": "query.property('ptd_dbb:move_list') == 'grimhowl_roar' || query.property('ptd_dbb:spawning')"}, {"arrow": "query.property('ptd_dbb:arrow_hit_1')"}, {"arrow1": "query.property('ptd_dbb:arrow_hit_2')"}, {"arrow2": "query.property('ptd_dbb:arrow_hit_3')"}, {"arrow3": "query.property('ptd_dbb:arrow_hit_4')"}, {"arrow4": "query.property('ptd_dbb:arrow_hit_5')"}, {"arrow5": "query.property('ptd_dbb:arrow_hit_6')"}, {"arrow6": "query.property('ptd_dbb:arrow_hit_7')"}, {"arrow7": "query.property('ptd_dbb:arrow_hit_8')"}, {"arrow8": "query.property('ptd_dbb:arrow_hit_9')"}, {"arrow9": "query.property('ptd_dbb:arrow_hit_10')"}, {"arrow10": "query.property('ptd_dbb:arrow_hit_11')"}, {"arrow11": "query.property('ptd_dbb:arrow_hit_12')"}, {"effect": "0.0"}], "is_hurt_color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 0.0}, "overlay_color": {"r": "query.property('ptd_dbb:death_timer')/80", "g": "query.property('ptd_dbb:death_timer')/80", "b": "query.property('ptd_dbb:death_timer')/80", "a": "query.property('ptd_dbb:death_timer')/80"}}}}