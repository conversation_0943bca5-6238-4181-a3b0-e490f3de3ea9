{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.winged_zombie.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": ["idle"], "transitions": [{"moving": "q.ground_speed > 0.3"}, {"attacking": "q.is_delayed_attacking"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "moving": {"animations": ["move"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"attacking": "q.is_delayed_attacking"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "attacking": {"animations": ["attack"], "transitions": [{"idling": "!q.is_delayed_attacking || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}