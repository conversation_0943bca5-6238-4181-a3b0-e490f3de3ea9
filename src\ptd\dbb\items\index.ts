import { ItemComponentRegistry } from "@minecraft/server";
import { piglinChampionAxeOnUse } from "./weapons/piglinChampionAxe";
import { piglinChampionGobletOnUse } from "./other/piglinChampionGoblet";

// Global Item Custom Component Initialization
export function initializeItemCustomComponents(data: ItemComponentRegistry) {
    data.registerCustomComponent("ptd_dbb:item", {
        onUse(ev) {
            const item = ev.itemStack;
            const player = ev.source;

            // Skip if no item or player
            if (!item || !player) return;
            const itemTypeId = item.type.id;

            switch (itemTypeId) {
                case 'ptd_dbb:piglin_champion_axe':
                    piglinChampionAxeOnUse(player, item);
                    break;

                case 'ptd_dbb:piglin_champion_goblet':
                    piglinChampionGobletOnUse(player, item);
                    break;
                default:
                    break;
            }
            return;
        }
    });
}