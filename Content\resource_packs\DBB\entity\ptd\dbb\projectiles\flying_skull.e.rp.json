{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:flying_skull", "materials": {"default": "entity"}, "textures": {"default": "textures/ptd/dbb/entity/projectiles/flying_skull"}, "geometry": {"default": "geometry.ptd_dbb_flying_skull"}, "animations": {"rotate": "animation.ptd_dbb.projectile.rotate", "flying": "animation.ptd_dbb.flying_skull.flying"}, "scripts": {"pre_animation": ["v.x_rot = q.property('ptd_dbb:rotation_x');", "v.y_rot = q.property('ptd_dbb:rotation_y');"], "animate": ["flying", "rotate"]}, "render_controllers": ["controller.render.ptd_dbb.default_emissive"]}}}