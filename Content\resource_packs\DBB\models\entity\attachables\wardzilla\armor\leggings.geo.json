{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ptd_dbb_wardzilla_leggings", "texture_width": 32, "texture_height": 32, "visible_bounds_width": 3, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "body", "pivot": [0, 24, 0], "cubes": [{"origin": [4, 5, -0.25], "size": [3, 6, 0], "pivot": [4, 8, -0.25], "rotation": [0, -45, 0], "uv": [10, 19]}, {"origin": [-4, 12, -2], "size": [8, 3, 4], "inflate": 0.4, "uv": [0, 0]}, {"origin": [-1.25, 14, 5.75], "size": [1, 1, 2], "pivot": [-2, 17, 1.75], "rotation": [0, 45, 0], "uv": [24, 4]}, {"origin": [-1, 12.5, -3], "size": [2, 1, 1], "uv": [14, 26]}, {"origin": [-1.5, 13.5, -2.6], "size": [3, 3, 1], "inflate": 0.15, "pivot": [0, 15, -2.1], "rotation": [0, 0, 45], "uv": [24, 0]}, {"origin": [-0.75, 14.25, -3.3], "size": [2, 2, 1], "pivot": [0, 15.5, -2.3], "rotation": [0, 0, 45], "uv": [8, 25]}, {"origin": [-2, 11.91124, 1.61886], "size": [4, 4, 4], "inflate": 0.1, "uv": [16, 7]}, {"origin": [0, 16, 2.75], "size": [0, 2, 3], "uv": [24, 22]}, {"origin": [-1.5, 12.5, 4.25], "size": [3, 3, 4], "pivot": [0, 14, 6.25], "rotation": [22.5, 0, 0], "uv": [16, 15]}, {"origin": [-1, 14, 6.75], "size": [2, 2, 3], "pivot": [0, 15, 8.25], "rotation": [45, 0, 0], "uv": [0, 19]}, {"origin": [0.001, 16.26777, 7.39645], "size": [0, 1, 1], "pivot": [0.001, 16.76777, 7.89645], "rotation": [45, 0, 0], "uv": [24, 27]}, {"origin": [0.001, 16.15119, 6.02373], "size": [0, 1, 1], "pivot": [-1.5, 16.15119, 7.52373], "rotation": [22.5, 0, 0], "uv": [24, 27]}, {"origin": [1.5, 9.5, 2], "size": [1, 1, 1], "inflate": 0.1, "pivot": [2, 10, 2], "rotation": [22.5, 0, 0], "uv": [20, 26]}, {"origin": [-7, 5, -0.25], "size": [3, 6, 0], "pivot": [-4, 8, -0.25], "rotation": [0, 45, 0], "uv": [10, 19], "mirror": true}, {"origin": [0.25, 14, 5.75], "size": [1, 1, 2], "pivot": [2, 17, 1.75], "rotation": [0, -45, 0], "uv": [24, 4], "mirror": true}]}, {"name": "rightLeg", "parent": "body", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-4, 4, -2], "size": [4, 8, 4], "inflate": 0.3, "uv": [0, 7], "mirror": true}, {"origin": [-2.5, 5, 2], "size": [1, 1, 1], "inflate": 0.1, "pivot": [-2, 5.5, 2], "rotation": [22.5, 0, 0], "uv": [20, 26], "mirror": true}, {"origin": [-3, 6.5, 1.75], "size": [2, 2, 2], "inflate": 0.1, "pivot": [-2, 7.5, 1.75], "rotation": [22.5, 0, 0], "uv": [16, 22], "mirror": true}, {"origin": [-3, 7, -2.75], "size": [2, 2, 2], "inflate": 0.1, "pivot": [-2, 8, -1.75], "rotation": [90, 0, 0], "uv": [0, 24], "mirror": true}, {"origin": [-2.5, 9.5, 2], "size": [1, 1, 1], "inflate": 0.1, "pivot": [-2, 10, 2], "rotation": [22.5, 0, 0], "uv": [20, 26], "mirror": true}]}, {"name": "leftLeg", "parent": "body", "pivot": [1.9, 12, 0], "mirror": true, "cubes": [{"origin": [0, 4, -2], "size": [4, 8, 4], "inflate": 0.3, "uv": [0, 7], "mirror": false}, {"origin": [1.5, 5, 2], "size": [1, 1, 1], "inflate": 0.1, "pivot": [2, 5.5, 2], "rotation": [22.5, 0, 0], "uv": [20, 26], "mirror": false}, {"origin": [1, 6.5, 1.75], "size": [2, 2, 2], "inflate": 0.1, "pivot": [2, 7.5, 1.75], "rotation": [22.5, 0, 0], "uv": [16, 22], "mirror": false}, {"origin": [1, 7, -2.75], "size": [2, 2, 2], "inflate": 0.1, "pivot": [2, 8, -1.75], "rotation": [90, 0, 0], "uv": [0, 24], "mirror": false}]}]}]}