import { Entity, system } from "@minecraft/server";
import { spawnEntitiesWithInterval, EntityQuantityConfig } from "../../../utilities/summonEntity";
import { getRandomLocation } from "../../../utilities/vector3";

/**
 * Attack timing constants for undead summon attack phases
 */
const SUMMON_START_TIMING = 40; // Start summoning zombies at tick 40

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 115; // Total attack duration is 115 ticks

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Configuration for the undead summon attack
 */
const UNDEAD_SUMMON_CONFIG = {
  /** Summoning duration in ticks */
  SUMMON_DURATION: 40,
  /** Number of minions to summon */
  MINION_COUNT: 3
};

/**
 * Executes the undead summon attack for the Necromancer using the new timing system
 * Uses localized runTimeout for summoning, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeUndeadSummonAttack(necromancer: Entity): void {
  // Start summoning at tick 40
  let summonTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(summonTiming);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "undead_summon") {
        performUndeadSummon(necromancer);
      }
    } catch (error) {
      system.clearRun(summonTiming);
    }
  }, SUMMON_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "undead_summon") {
        necromancer.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      necromancer.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the undead summoning logic
 * @param necromancer The necromancer entity
 */
async function performUndeadSummon(necromancer: Entity): Promise<void> {
  try {
    // Configure the entities to spawn randomly
    // Generate a random count for winged zombies
    const wingedZombieCount = Math.floor(Math.random() * UNDEAD_SUMMON_CONFIG.MINION_COUNT);

    // The zombie brute count is the remaining minions to reach the total
    const zombieBruteCount = UNDEAD_SUMMON_CONFIG.MINION_COUNT - wingedZombieCount;

    const entityConfigs: EntityQuantityConfig[] = [
      {
        entityId: "ptd_dbb:winged_zombie",
        count: wingedZombieCount
      },
      {
        entityId: "ptd_dbb:zombie_brute",
        count: zombieBruteCount
      }
    ].filter((config) => config.count > 0);

    // Spawn the minions with a delay between each
    await spawnEntitiesWithInterval(
      necromancer.dimension,
      entityConfigs,
      () => {
        // Get a random position around the necromancer
        const pos = getRandomLocation(
          necromancer.location,
          necromancer.dimension,
          3, // Base offset (minimum distance from necromancer)
          4, // Additional random offset
          0, // No Y offset
          true // Check for air block
        );

        // Add visual effects if position is valid
        if (pos) {
          necromancer.dimension.spawnParticle("minecraft:large_explosion", pos);
        }

        return pos;
      },
      Math.floor(UNDEAD_SUMMON_CONFIG.SUMMON_DURATION / UNDEAD_SUMMON_CONFIG.MINION_COUNT), // Delay between spawns
      (entity) => {
        // Play sound effect when entity is spawned
        necromancer.dimension.playSound("mob.zombie.spawn", entity.location);
      }
    );
  } catch (error) {
    console.warn(`Error in undead summon attack: ${error}`);
  }
}
