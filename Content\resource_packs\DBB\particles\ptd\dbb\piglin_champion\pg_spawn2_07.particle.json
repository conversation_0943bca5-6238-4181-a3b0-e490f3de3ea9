{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "ptd_dbb:pg_spawn2_07", "basic_render_parameters": {"material": "particles_blend", "texture": "textures/ptd/dbb/particles/bosses/piglin_champion/dot"}}, "curves": {"variable.rainbow": {"type": "catmull_rom", "input": "variable.particle_random_2", "horizontal_range": 1, "nodes": [1, 2.92, 1, 1.18]}, "variable.psize": {"type": "catmull_rom", "input": "variable.particle_age", "horizontal_range": "variable.particle_lifetime", "nodes": [0, 0, 1, 0.14, 0]}}, "components": {"minecraft:emitter_initialization": {"creation_expression": "variable.radius = 2.5;"}, "minecraft:emitter_rate_steady": {"spawn_rate": 20, "max_particles": 100}, "minecraft:emitter_lifetime_looping": {"active_time": 1, "sleep_time": 3}, "minecraft:emitter_shape_sphere": {"offset": ["math.sin(variable.emitter_age * 0) * (0-variable.particle_random_2)", 0.15, 0], "radius": "math.random(7,8)", "surface_only": true, "direction": "outwards"}, "minecraft:particle_lifetime_expression": {"max_lifetime": "math.random(0.7,1)"}, "minecraft:particle_initial_speed": 5, "minecraft:particle_motion_dynamic": {"linear_acceleration": [0, 5, 0], "linear_drag_coefficient": "math.random(0.1,0.8)"}, "minecraft:particle_appearance_billboard": {"size": ["0.9* v.psize", "0.9 * v.psize"], "facing_camera_mode": "rotate_xyz", "uv": {"texture_width": 16, "texture_height": 16, "uv": [0, 0], "uv_size": [16, 16]}}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age/v.particle_lifetime", "gradient": {"0.0": "#FF545454", "1.0": "#FF656565"}}}}}}