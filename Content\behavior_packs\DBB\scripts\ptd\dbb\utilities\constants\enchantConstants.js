export const helmetEnchantments = new Set([
    { id: 'minecraft:protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:fire_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:blast_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:projectile_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:thorns', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:respiration', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:aqua_affinity', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const chestplateEnchantments = new Set([
    { id: 'minecraft:protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:fire_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:blast_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:projectile_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:thorns', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const leggingsEnchantments = new Set([
    { id: 'minecraft:protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:fire_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:blast_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:projectile_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:thorns', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:swift_sneak', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const bootEnchantments = new Set([
    { id: 'minecraft:protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:fire_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:blast_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:projectile_protection', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:thorns', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:depth_strider', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:feather_falling', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:frost_walker', minLevel: 1, maxLevel: 2 },
    { id: 'minecraft:soul_speed', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const swordEnchantments = new Set([
    { id: 'minecraft:sharpness', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:smite', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:bane_of_arthropods', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:knockback', minLevel: 1, maxLevel: 2 },
    { id: 'minecraft:fire_aspect', minLevel: 1, maxLevel: 2 },
    { id: 'minecraft:looting', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const pickaxeEnchantments = new Set([
    { id: 'minecraft:efficiency', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:fortune', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:silk_touch', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const axeEnchantments = new Set([
    { id: 'minecraft:efficiency', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:fortune', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:silk_touch', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:sharpness', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:smite', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:bane_of_arthropods', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const hoeEnchantments = new Set([
    { id: 'minecraft:efficiency', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:fortune', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:silk_touch', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const shovelEnchantments = new Set([
    { id: 'minecraft:efficiency', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:fortune', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:silk_touch', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const fishingRodEnchantments = new Set([
    { id: 'minecraft:lure', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:luck_of_the_sea', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const flintAndSteelEnchantments = new Set([
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const shearsEnchantments = new Set([
    { id: 'minecraft:efficiency', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const shieldEnchantments = new Set([
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const bowEnchantments = new Set([
    { id: 'minecraft:power', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:punch', minLevel: 1, maxLevel: 2 },
    { id: 'minecraft:flame', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:infinity', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const crossbowEnchantments = new Set([
    { id: 'minecraft:multishot', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:piercing', minLevel: 1, maxLevel: 4 },
    { id: 'minecraft:quick_charge', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const tridentEnchantments = new Set([
    { id: 'minecraft:loyalty', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:channeling', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:impaling', minLevel: 1, maxLevel: 5 },
    { id: 'minecraft:riptide', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 }
]);
export const maceEnchantments = new Set([
    { id: 'minecraft:unbreaking', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:mending', minLevel: 1, maxLevel: 1 },
    { id: 'minecraft:wind_burst', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:density', minLevel: 1, maxLevel: 3 },
    { id: 'minecraft:breach', minLevel: 1, maxLevel: 3 }
]);
export const mutuallyExclusiveGroups = [
    {
        group: "protection_types",
        enchantments: [
            "minecraft:protection",
            "minecraft:projectile_protection",
            "minecraft:blast_protection",
            "minecraft:fire_protection"
        ]
    },
    {
        group: "sword_damage",
        enchantments: [
            "minecraft:sharpness",
            "minecraft:smite",
            "minecraft:bane_of_arthropods"
        ]
    },
    {
        group: "tool_fortune",
        enchantments: [
            "minecraft:fortune",
            "minecraft:silk_touch"
        ]
    },
    {
        group: "crossbow_projectiles",
        enchantments: [
            "minecraft:multishot",
            "minecraft:piercing"
        ]
    },
    {
        group: "trident_launch",
        enchantments: [
            "minecraft:riptide",
            "minecraft:loyalty",
            "minecraft:channeling"
        ]
    },
    {
        group: "infinite_mending",
        enchantments: [
            "minecraft:infinity",
            "minecraft:mending"
        ]
    },
    {
        group: "boot_water_walk",
        enchantments: [
            "minecraft:frost_walker",
            "minecraft:depth_strider"
        ]
    }
];
