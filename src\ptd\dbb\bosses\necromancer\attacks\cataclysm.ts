import { Entity, EntityDamageCause, Vector3, system } from "@minecraft/server";
import { NECROMANCER_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

/**
 * Configuration for the cataclysm attack
 */
const CATACLYSM_CONFIG = {
  /** Maximum radius the attack can reach in blocks */
  MAX_RADIUS: 32,
  /** Starting radius of the attack in blocks */
  MIN_RADIUS: 1,
  /**
   * Growth rate factor - determines how quickly the radius reaches maximum size
   * Value between 0-1 representing the percentage of damage phase at which radius reaches maximum
   * Example: 0.5 means radius reaches maximum at 50% of the damage phase
   * Lower values = faster growth
   */
  GROWTH_RATE: 0.5
};

/**
 * Attack timing constants for cataclysm attack phases
 */
const DAMAGE_START_TIMING = 70; // Start damage at tick 70 (3.5 seconds)
const DAMAGE_END_TIMING = 150; // End damage at tick 150 (7.5 seconds)

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 200; // 10 seconds

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Executes the cataclysm attack for the Necromancer using the new timing system
 * Uses localized runTimeout for damage phase, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeCataclysmAttack(necromancer: Entity): void {
  // Start damage phase at tick 70
  let damagePhaseInterval: number;

  let damageStartTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageStartTiming);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "cataclysm") {
        // Start continuous damage phase
        let currentTick = DAMAGE_START_TIMING;
        damagePhaseInterval = system.runInterval(() => {
          try {
            const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
            if (isDead || necromancer.getProperty("ptd_dbb:attack") !== "cataclysm") {
              system.clearRun(damagePhaseInterval);
              return;
            }

            if (currentTick <= DAMAGE_END_TIMING) {
              performCataclysmDamage(necromancer, currentTick);
              currentTick++;
            } else {
              system.clearRun(damagePhaseInterval);
            }
          } catch (error) {
            system.clearRun(damagePhaseInterval);
          }
        }, 1);
      }
    } catch (error) {
      system.clearRun(damageStartTiming);
    }
  }, DAMAGE_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        if (damagePhaseInterval) system.clearRun(damagePhaseInterval);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "cataclysm") {
        necromancer.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      necromancer.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the cataclysm damage calculation for a specific tick
 * @param necromancer The necromancer entity
 * @param attackTimer The current attack timer
 */
function performCataclysmDamage(necromancer: Entity, attackTimer: number): void {
  try {
    // Calculate the current radius based on the attack timer
    // Get total duration of damage phase
    const damagePhaseStart = 70; // 3.5 seconds
    const damagePhaseEnd = 150; // 7.5 seconds
    const totalDamageTicks = damagePhaseEnd - damagePhaseStart;

    // Calculate normalized progress (0 to 1 over the damage phase)
    const normalizedProgress = (attackTimer - damagePhaseStart) / totalDamageTicks;

    // Scale progress based on growth rate - reaches 1.0 when normalizedProgress equals GROWTH_RATE
    // This makes the radius reach maximum size earlier in the damage phase
    const scaledProgress = Math.min(normalizedProgress / CATACLYSM_CONFIG.GROWTH_RATE, 1);

    // Calculate current radius - starts at MIN_RADIUS and grows to MAX_RADIUS
    const currentRadius =
      CATACLYSM_CONFIG.MIN_RADIUS + scaledProgress * (CATACLYSM_CONFIG.MAX_RADIUS - CATACLYSM_CONFIG.MIN_RADIUS);

    // Use the necromancer's location as the origin for the attack
    const originPos: Vector3 = necromancer.location;

    // Apply damage to nearby entities
    necromancer.dimension
      .getEntities({
        location: originPos,
        maxDistance: currentRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["necromancer"]
      })
      .forEach((entity) => {
        // Use direct damage value instead of percentage
        const damage = NECROMANCER_ATTACK_DAMAGES.cataclysm.damage;
        entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: necromancer });

        // Spawn soul particles at the entity's location for visual effect
        necromancer.dimension.spawnParticle("minecraft:soul", entity.location);
      });

    // Spawn particles at the edge of the current radius for visual effect
    spawnRadiusParticles(necromancer, originPos, currentRadius);
  } catch (error) {}
}

/**
 * Spawns particles around the perimeter of the current radius
 *
 * @param necromancer The necromancer entity
 * @param center The center position of the radius
 * @param radius The current radius
 */
function spawnRadiusParticles(necromancer: Entity, center: Vector3, radius: number): void {
  try {
    // Number of particles to spawn scales with radius
    const particleCount = Math.floor(radius * 4);

    // Spawn particles in a circle around the perimeter
    for (let i = 0; i < particleCount; i++) {
      const angle = (i / particleCount) * Math.PI * 2;
      const x = center.x + Math.cos(angle) * radius;
      const z = center.z + Math.sin(angle) * radius;

      // Vary the y position slightly for visual effect
      const y = center.y + (Math.random() * 2 - 1);

      // Spawn soul particle
      necromancer.dimension.spawnParticle("minecraft:soul_particle", { x, y, z });
    }
  } catch (error) {
    // Handle errors silently since it's only summoning particles
  }
}
