/*
* Grimhowl Shadow Onslaught Attack
* This function handles the Shadow Onslaught attack of the Grim Howl boss.
* It teleports the boss to a random position around the target and applies damage.
* The attack consists of a 3-hit combo with sound effects and animations.
*/

import { system, Vector3, Entity, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { grimHowlTargetsToCheck } from "../constants";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

/**
* Handles the Shadow Onslaught attack of the Grimhowl boss.
* @param {Entity} sourceEntity - The entity performing the attack.
*/

export function doGrimhowlShadowOnslaught(sourceEntity: Entity): void {
    try {
        if (!sourceEntity) return;

        const nearbyTargets: Entity[] = grimHowlTargetsToCheck.flatMap((family: string) =>
            sourceEntity.dimension.getEntities({
                location: sourceEntity.location,
                families: [family],
                maxDistance: 64
            }).filter(filterValidTargets(sourceEntity))
        );
    
        const targetEntity: Entity = nearbyTargets[nearbyTargets.length - 1] as Entity;
    
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_shadow_onslaught');
        sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
    
        if (!targetEntity) {
            sourceEntity.triggerEvent('ptd_dbb:attack_done');
            return;
        }
    
        const targetLocation: Vector3 = targetEntity.location;
        const swordMode: boolean = !!sourceEntity.getProperty('ptd_dbb:sword_mode');
        const offset: number = swordMode ? 5 : 2.5;
        const positions: Vector3[] = [
            { x: targetLocation.x + offset, y: targetLocation.y, z: targetLocation.z }, // East
            { x: targetLocation.x - offset, y: targetLocation.y, z: targetLocation.z }, // West
            { x: targetLocation.x, y: targetLocation.y, z: targetLocation.z + offset }, // South
            { x: targetLocation.x, y: targetLocation.y, z: targetLocation.z - offset }  // North
        ];
    
        // Teleport to a random position around the target after 1 second
        system.runTimeout(() => {
            if (!sourceEntity) return;
        
            const randomPosition: Vector3 = positions[Math.floor(Math.random() * positions.length)] as Vector3;
            sourceEntity.teleport(randomPosition, { facingLocation: targetEntity.location, keepVelocity: true });
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
        }, 19); // 1 second = 20 ticks
    
        // Perform a 3-hit combo with 0.25-second intervals
        for (let i = 0; i < 3; i++) {
            system.runTimeout(() => {
                if (!sourceEntity) return;
            
                const viewDirection: Vector3 = sourceEntity.getViewDirection();
                const impulse: Vector3 = {
                    x: viewDirection.x * 1,
                    y: 0.1,
                    z: viewDirection.z * 1
                };
                sourceEntity.applyImpulse(impulse);
            
                const forwardLocation: Vector3 = {
                    x: sourceEntity.location.x + viewDirection.x * offset,
                    y: sourceEntity.location.y,
                    z: sourceEntity.location.z + viewDirection.z * offset
                };
            
                const collideTargets: Entity[] = sourceEntity.dimension.getEntities({
                    location: sourceEntity.location,
                    excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                    maxDistance: 4.4
                }).filter(filterValidTargets(sourceEntity));
            
                const frontTargets: Entity[] = sourceEntity.dimension.getEntities({
                    location: forwardLocation,
                    excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                    maxDistance: 4.4
                }).filter(filterValidTargets(sourceEntity));
            
                collideTargets.forEach((entity: Entity) => {
                    entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.collision_damage.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                });
            
                frontTargets.forEach((entity: Entity) => {
                    entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.shadow_onslaught.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                });
            
                sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
            }, 20 + i * 5); // Start at 1 second, then add 0.25 seconds (5 ticks) for each hit
        }
    
        const delays: number[] = [23, 28, 33]; // Delays in ticks for 1.13, 1.38, and 1.63 seconds
    
        for (let i = 0; i < delays.length; i++) {
            system.runTimeout(() => {
                if (!sourceEntity || !targetEntity) {
                    return;
                }
                if (swordMode) {
                    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                } else {
                    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                    sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
                }
            }, delays[i]);
        }
    } catch (error) {
        sourceEntity?.triggerEvent('ptd_dbb:attack_done');
    }
    
}
