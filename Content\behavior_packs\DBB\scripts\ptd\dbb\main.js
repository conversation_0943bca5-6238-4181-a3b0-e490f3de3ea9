import { Block, Entity, system, world } from "@minecraft/server";
import { handleCameraShakeScriptEvent, cameraShake } from "./bosses/general_effects/camerashake";
import { initializeItemCustomComponents } from "./items/index";
import { checkBossSummoningRequirements, hasRelevantBaseItems } from "./bosses/general_mechanics/bossSummoner";
import { grimhowlMechanics } from "./bosses/grimhowl/index";
import { shockwave } from "./bosses/general_attacks/shockwave";
import "./entities/index"; // Import entities module for initialization
import "./player/index"; // Import player module for initialization
// Creates a Set of dimension references for quick lookup.
export const dimensions = new Set(["overworld", "nether", "the_end"].map((dimension) => world.getDimension(dimension)));
// Initialize custom components
world.beforeEvents.worldInitialize.subscribe((data) => {
    const itemComponentRegistry = data.itemComponentRegistry;
    initializeItemCustomComponents(itemComponentRegistry);
});
/**
 * Main game loop initialization
 * Sets up event listeners and periodic updates for boss mechanics
 */
system.runTimeout(() => {
    // Schedule a timeout to start the interval
    // MAIN TICK
    system.runInterval(() => {
        dimensions.forEach((dimension) => {
            // Iterate over each dimension
            // Only check for boss summoning requirements if there are relevant base items present
            if (hasRelevantBaseItems(dimension)) {
                checkBossSummoningRequirements(dimension);
            }
        });
    });
    // Listen for script events
    system.afterEvents.scriptEventReceive.subscribe((data) => {
        const entity = data.sourceEntity;
        const block = data.sourceBlock;
        const eventId = data.id;
        if (entity)
            scriptEventDispatcher(entity, eventId);
        if (block)
            scriptEventDispatcher(block, eventId);
        return;
    });
    function scriptEventDispatcher(source, eventId) {
        if (source instanceof Entity) {
            const entityTypeId = source.typeId;
            switch (eventId) {
                // Handle camera shake events
                case "ptd_dbb:camerashake":
                    handleCameraShakeScriptEvent(source);
                    break;
                // Handle horizontal attack events
                case "ptd_dbb:horizontal_attack_end":
                    if (source.typeId === "ptd_dbb:piglin_champion") {
                        source.triggerEvent("ptd_dbb:reset_attack");
                    }
                    break;
                // Handle vertical attack events
                case "ptd_dbb:vertical_attack_end":
                    if (source.typeId === "ptd_dbb:piglin_champion") {
                        source.triggerEvent("ptd_dbb:reset_attack");
                    }
                    break;
                // Handle piglin champion spawning effects
                case "ptd_dbb:piglin_champion_spawn_shockwave":
                    if (source.typeId === "ptd_dbb:piglin_champion") {
                        // Apply shockwave with radius 8, power 1.5, damage 8, excluding piglin_champion family
                        shockwave(source, 5, 1.5, 8, ["piglin_champion"]);
                        // Apply camera shake effect
                        cameraShake(source, 32, 0.02, 0.5, 0.5);
                        // Play a particle effect at the piglin's location
                        source.dimension.spawnParticle("minecraft:large_explosion", source.location);
                        // Play a sound effect
                        source.dimension.playSound("random.explode", source.location);
                    }
                    break;
                case "ptd_dbb:piglin_champion_spawn_particle":
                    if (source.typeId === "ptd_dbb:piglin_champion") {
                        // Play the final particle effect at the piglin's location
                        source.dimension.spawnParticle("ptd_dbb:pg_spawn3_01", source.location);
                    }
                    break;
                default:
                    break;
            }
            if (entityTypeId === "ptd_dbb:grimhowl") {
                grimhowlMechanics({ id: eventId, sourceEntity: source }, "script");
            }
        }
        else if (source instanceof Block) {
            // Handle block-specific script events
            return; // Just return for now since we don't have any custom blocks that fire scriptevents
        }
        return;
    }
}, 60); // Initial delay of 60 ticks before starting the interval
