{
	"format_version": "1.10.0",
	"minecraft:client_entity": {
		"description": {
			"identifier": "ptd_dbb:grimhowl",
			"materials": {
				"default": "entity_emissive_alpha",
				"effects": "entity_beam_additive"
			},
			"textures": {
				"default": "textures/ptd/dbb/bosses/grimhowl/grimhowl",
				"enraged": "textures/ptd/dbb/bosses/grimhowl/grimhowl_enraged"
			},
			"geometry": {
				"default": "geometry.ptd_dbb_grimhowl",
				"effects": "geometry.ptd_dbb_grimhowl_effects"
			},
			"animations": {
				"basic_root": "controller.animation.ptd_dbb.grimhowl.basic_root",
				"runsprintcontroller": "controller.animation.ptd_dbb.grimhowl.runsprintcontroller",
				"move_list_root": "controller.animation.ptd_dbb.grimhowl.move_list_root",

				"look": "animation.ptd_dbb.grimhowl.look",
				"spawn": "animation.ptd_dbb.grimhowl.entrance",
				"is_not_attacking": "animation.ptd_dbb.grimhowl.is_not_attacking",

				"idle": "animation.ptd_dbb.grimhowl.idle",
				"idle_swordless": "animation.ptd_dbb.grimhowl.idle_swordless",
				"walk": "animation.ptd_dbb.grimhowl.walk",
				"sprint": "animation.ptd_dbb.grimhowl.sprint",
				"death": "animation.ptd_dbb.grimhowl.death",

				//NORMAL
				"grimhowl_slash": "animation.ptd_dbb.grimhowl.attack_sword",
				"grimhowl_spinning_slash": "animation.ptd_dbb.grimhowl.attack_sword_spin",
				"grimhowl_pounce": "animation.ptd_dbb.grimhowl.pounce_attack",
				"grimhowl_claw_left": "animation.ptd_dbb.grimhowl.claw_left",
				"grimhowl_claw_right": "animation.ptd_dbb.grimhowl.claw_right",
				"grimhowl_backstep_sword": "animation.ptd_dbb.grimhowl.dodge_sword_attack",

				//SWORDLESS
				"swordless_grimhowl_slash": "animation.ptd_dbb.grimhowl.attack_sword_swordless",
				"swordless_grimhowl_spinning_slash": "animation.ptd_dbb.grimhowl.attack_sword_spin_swordless",
				"swordless_grimhowl_pounce": "animation.ptd_dbb.grimhowl.pounce_attack_swordless",
				"swordless_grimhowl_claw_left": "animation.ptd_dbb.grimhowl.swordless_claw_left",
				"swordless_grimhowl_claw_right": "animation.ptd_dbb.grimhowl.swordless_claw_right",
				"grimhowl_backstep": "animation.ptd_dbb.grimhowl.dodge",

				//ENRAGED
				"grimhowl_shadow_onslaught": "animation.ptd_dbb.grimhowl.shadow_onslaught",
				"swordless_grimhowl_shadow_onslaught": "animation.ptd_dbb.grimhowl.shadow_onslaught_swordless",

				//EXTRAS
				"grimhowl_roar": "animation.ptd_dbb.grimhowl.roar",
				"grimhowl_arrow_shake": "animation.ptd_dbb.grimhowl.arrow_shake",
				"grimhowl_swordless_transition": "animation.ptd_dbb.grimhowl.swordless_transition",
				"grimhowl_sword_transition": "animation.ptd_dbb.grimhowl.sword_transition"
			},
			"sound_effects": {
				"roar": "mob.ptd_dbb_grimhowl.roar",
				"spawn": "mob.ptd_dbb_grimhowl.spawn",
				"dodge": "mob.ptd_dbb_grimhowl.dodge"
			},
			"scripts": {
				"should_update_bones_and_effects_offscreen": true,
				"animate": [
					{
						"is_not_attacking": "query.property('ptd_dbb:move_list') == 'none' && !query.property('ptd_dbb:spawning')"
					},
					"basic_root",
					"look"
				]
			},
			"render_controllers": [
				"controller.render.ptd_dbb.grimhowl",
				{"controller.render.ptd_dbb.grimhowl_effects": "query.is_alive"}
			],
            "spawn_egg": {
                "base_color": "#0a012c",
                "overlay_color": "#2c214c"
            }
		}
	}
}