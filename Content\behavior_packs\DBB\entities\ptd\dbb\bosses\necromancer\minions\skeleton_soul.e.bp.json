{
  "format_version": "1.21.80",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_dbb:skeleton_soul",
      "is_spawnable": true,
      "is_summonable": true,
      "properties": {
        "ptd_dbb:spawning": {
          "type": "bool",
          "client_sync": true,
          "default": true
        },
        "ptd_dbb:dead": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_dbb:death_timer": {
          "type": "int",
          "client_sync": true,
          "default": 0,
          "range": [0, 100]
        }
      }
    },
    "component_groups": {
      "ptd_dbb:spawning": {
        "minecraft:physics": {},
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 1.2083,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:on_spawn",
            "target": "self"
          }
        }
      },
      "ptd_dbb:default": {
        "minecraft:physics": {},
        "minecraft:attack": {
          "damage": 10
        },
        "minecraft:movement": {
          "value": 0.25
        },
        "minecraft:movement.basic": {},
        "minecraft:jump.static": {},
        "minecraft:navigation.walk": {
          "can_path_over_water": true,
          "avoid_damage_blocks": true,
          "can_break_doors": true,
          "can_pass_doors": true,
          "can_walk": true
        },
        "minecraft:annotation.break_door": {
          "break_time": 10,
          "min_difficulty": "normal"
        },
        "minecraft:behavior.break_door": {
          "priority": 4
        },
        "minecraft:behavior.delayed_attack": {
          "priority": 1,
          "attack_once": false,
          "track_target": true,
          "require_complete_path": false,
          "random_stop_interval": 0,
          "reach_multiplier": 1.1,
          "speed_multiplier": 1.15,
          "attack_duration": 0.2917,
          "hit_delay_pct": 0.4,
          "cooldown_time": 1
        },
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 0,
          "must_see": false,
          "attack_interval": 1,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "reevaluate_description": true,
          "entity_types": [
            {
              "priority": 0,
              "max_dist": 64,
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "player"
              }
            },
            {
              "priority": 1, // Have the minion fight other minions first before targeting other bosses
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "minion"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "necromancer"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            },
            {
              "priority": 2,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "boss"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "necromancer"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            }
          ]
        },
        "minecraft:behavior.float": {
          "priority": 2
        },
        "minecraft:behavior.random_look_around": {
          "priority": 3
        },
        "minecraft:behavior.random_stroll": {
          "priority": 5,
          "speed_multiplier": 0.8
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 2
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "domain": "ptd_dbb:dead",
                  "subject": "self",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "is_family",
                  "subject": "other",
                  "value": "necromancer"
                }
              },
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:dead": {
        "minecraft:physics": {},
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 1.0,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:despawn",
            "target": "self"
          }
        },
        "minecraft:movement": {
          "max": 0
        },
        "minecraft:behavior.random_look_around": {
          "priority": 999999
        },
        "minecraft:behavior.random_stroll": {
          "priority": 999999
        },
        "minecraft:body_rotation_blocked": {},
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 2
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "domain": "ptd_dbb:dead",
                  "subject": "self",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "is_family",
                  "subject": "other",
                  "value": "necromancer"
                }
              },
              "deals_damage": "no"
            }
          ]
        },
        "minecraft:navigation.walk": {
          "can_path_over_water": false,
          "avoid_damage_blocks": false,
          "can_break_doors": false,
          "can_pass_doors": false,
          "can_walk": false
        }
      },
      "ptd_dbb:despawn": {
        "minecraft:instant_despawn": {}
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "add": {
          "component_groups": ["ptd_dbb:spawning"]
        }
      },
      "ptd_dbb:on_spawn": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:spawning"]
            }
          },
          {
            "set_property": {
              "ptd_dbb:spawning": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:dead": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:dead": true
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_dbb:default"]
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:dead"]
            }
          }
        ]
      },
      "ptd_dbb:despawn": {
        "sequence": [
          {
            "add": {
              "component_groups": ["ptd_dbb:despawn"]
            }
          }
        ]
      }
    },
    "components": {
      "minecraft:collision_box": {
        "width": 0.4,
        "height": 2
      },
      "minecraft:type_family": {
        "family": ["skeleton_soul", "undead", "monster", "hostile", "minion", "necromancer"]
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "test": "actor_health",
                "subject": "self",
                "operator": "<=",
                "value": 2
              },
              "event": "ptd_dbb:dead",
              "target": "self"
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:spawning",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:dead",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "necromancer"
              }
            },
            "deals_damage": "no"
          }
        ]
      },
      "minecraft:health": {
        "value": 16,
        "max": 16
      },
      "minecraft:variable_max_auto_step": {
        "base_value": 1.0625,
        "jump_prevented_value": 1.0625
      },
      "minecraft:follow_range": {
        "value": 128,
        "max": 128
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 4,
        "entity_types": {
          "max_dist": 64,
          "must_see": false,
          "priority": 0,
          "must_see_forget_duration": 0,
          "filters": {
            "none_of": [
              {
                "test": "is_family",
                "subject": "other",
                "operator": "!=",
                "value": "necromancer"
              }
            ]
          }
        }
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      },
      "minecraft:is_stackable": {},
      "minecraft:floats_in_liquid": {},
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
