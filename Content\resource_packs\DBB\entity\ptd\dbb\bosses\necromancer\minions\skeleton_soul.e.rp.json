{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:skeleton_soul", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest", "circle": "entity_emissive_alpha", "light": "emissive_translucent"}, "textures": {"default": "textures/ptd/dbb/entity/bosses/necromancer/minions/skeleton_soul"}, "geometry": {"default": "geometry.ptd_dbb_skeleton_soul"}, "animations": {"look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_dbb.skeleton_soul.spawn", "idle": "animation.ptd_dbb.skeleton_soul.idle", "walk": "animation.ptd_dbb.skeleton_soul.walk", "attack": "animation.ptd_dbb.skeleton_soul.attack", "death": "animation.ptd_dbb.skeleton_soul.death", "no_effects": "animation.ptd_dbb.skeleton_soul.no_effects", "general": "controller.animation.ptd_dbb.skeleton_soul.general", "attack_controller": "controller.animation.ptd_dbb.skeleton_soul.attack"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", "attack_controller", {"look_at_target": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}, {"no_effects": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}]}, "render_controllers": ["controller.render.ptd_dbb.skeleton_soul"], "spawn_egg": {"base_color": "#d2d2d2", "overlay_color": "#5c5c5c"}}}}