import { EntityComponentTypes, system } from "@minecraft/server";
import { getTarget } from "../../general_mechanics/targetUtils";
import { getDirection } from "../../../utilities/vector3";
import { faceDirection } from "../../../entities/projectileRotation";
/**
 * Attack timing constants for arcane blast attack phases
 */
const PROJECTILE_FIRE_TIMING = 50; // Fire skull at tick 50
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 100;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Configuration for the arcane blast attack
 */
const ARCANE_BLAST_CONFIG = {
    /** Projectile entity type */
    PROJECTILE_TYPE: "ptd_dbb:flying_skull",
    /** Projectile speed (velocity multiplier) */
    PROJECTILE_SPEED: 0.3,
    /** Tick interval for applying impulse */
    IMPULSE_INTERVAL: 1,
    /** Vertical offset from necromancer position */
    VERTICAL_OFFSET: -0.8
};
/**
 * Executes the arcane blast attack for the Necromancer using the new timing system
 * Uses localized runTimeout for projectile firing, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeArcaneBlastAttack(necromancer) {
    // Fire projectile at tick 50
    let projectileTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(projectileTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "arcane_blast") {
                fireArcaneBlastProjectile(necromancer);
            }
        }
        catch (error) {
            system.clearRun(projectileTiming);
        }
    }, PROJECTILE_FIRE_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "arcane_blast") {
                necromancer.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            necromancer.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Fires the arcane blast projectile
 * @param necromancer The necromancer entity
 */
function fireArcaneBlastProjectile(necromancer) {
    try {
        // Find the target once to determine initial direction
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
        if (!target)
            return;
        const headLoc = necromancer.getHeadLocation();
        const viewDir = necromancer.getViewDirection();
        // Spawn the flying skull projectile at the chest level of the necromancer
        const spawnPos = {
            x: headLoc.x + viewDir.x * 3,
            y: headLoc.y + ARCANE_BLAST_CONFIG.VERTICAL_OFFSET,
            z: headLoc.z + viewDir.z * 3
        };
        // Get the target location with a y-offset
        const targetLoc = {
            x: target.getHeadLocation().x,
            y: target.getHeadLocation().y + 0.2,
            z: target.getHeadLocation().z
        };
        // Calculate direction toward the target (already normalized)
        const direction = getDirection(spawnPos, targetLoc);
        // Calculate velocity based on direction and speed
        const velocity = {
            x: direction.x * ARCANE_BLAST_CONFIG.PROJECTILE_SPEED,
            y: direction.y * ARCANE_BLAST_CONFIG.PROJECTILE_SPEED,
            z: direction.z * ARCANE_BLAST_CONFIG.PROJECTILE_SPEED
        };
        // Spawn the flying skull entity
        const projectile = necromancer.dimension.spawnEntity(ARCANE_BLAST_CONFIG.PROJECTILE_TYPE, spawnPos);
        if (!projectile)
            return;
        // Set the projectile as an arcane blast projectile
        projectile.triggerEvent("ptd_dbb:as_arcane_blast");
        // Apply initial impulse
        const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
        if (!projectileComponent)
            return;
        // Apply initial impulse
        projectileComponent.shoot(velocity);
        // Set up interval to continuously apply impulse in the SAME direction
        const intervalId = system.runInterval(() => {
            try {
                // Check if entity is still valid/exists
                if (!projectile) {
                    system.clearRun(intervalId);
                    return;
                }
                // Apply impulse again using the SAME velocity vector
                const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
                if (projectileComponent) {
                    projectileComponent.shoot(velocity);
                }
                // Constantly face toward the direction
                faceDirection(projectile, spawnPos, targetLoc);
            }
            catch (error) {
                // If any error occurs (likely because projectile no longer exists)
                system.clearRun(intervalId);
            }
        }, ARCANE_BLAST_CONFIG.IMPULSE_INTERVAL);
        // Play a sound effect
        necromancer.dimension.playSound("mob.ptd_dbb_necromancer.arcane_blast_shoot", necromancer.location);
    }
    catch (error) {
        console.warn(`Error in arcane blast attack: ${error}`);
    }
}
