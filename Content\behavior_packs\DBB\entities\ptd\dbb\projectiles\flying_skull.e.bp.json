{"format_version": "1.21.80", "minecraft:entity": {"description": {"identifier": "ptd_dbb:flying_skull", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "properties": {"ptd_dbb:rotation_x": {"type": "float", "client_sync": true, "default": 0.0, "range": [-360.0, 360.0]}, "ptd_dbb:rotation_y": {"type": "float", "client_sync": true, "default": 0.0, "range": [-360.0, 360.0]}}}, "component_groups": {"ptd_dbb:normal_flying_skull": {"minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 4, "knockback": true, "semi_random_diff_damage": false}, "definition_event": {"event_trigger": {"event": "ptd_dbb:explode", "target": "self"}}}, "power": 1.2, "gravity": 0.0, "uncertainty_base": 0, "uncertainty_multiplier": 1, "anchor": 1, "offset": [0, -0.1, 0], "inertia": 0.98, "liquid_inertia": 1.0, "shoot_target": false}}, "ptd_dbb:aracane_blast_flying_skull": {"minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 4, "knockback": true, "semi_random_diff_damage": false}, "definition_event": {"event_trigger": {"event": "ptd_dbb:arcane_blast_explode", "target": "self"}}}, "power": 1.2, "gravity": 0.0, "uncertainty_base": 0, "uncertainty_multiplier": 1, "anchor": 1, "offset": [0, -0.1, 0], "inertia": 0.98, "liquid_inertia": 1.0, "shoot_target": false}}, "ptd_dbb:explode": {"minecraft:explode": {"breaks_blocks": false, "causes_fire": false, "destroy_affected_by_griefing": false, "fire_affected_by_griefing": false, "fuse_lit": true, "max_resistance": 0, "power": 3}}}, "events": {"ptd_dbb:normal": {"add": {"component_groups": ["ptd_dbb:normal_flying_skull"]}}, "ptd_dbb:as_arcane_blast": {"add": {"component_groups": ["ptd_dbb:aracane_blast_flying_skull"]}}, "ptd_dbb:explode": {"add": {"component_groups": ["ptd_dbb:explode"]}}, "ptd_dbb:arcane_blast_explode": {"add": {"component_groups": ["ptd_dbb:explode"]}}}, "components": {"minecraft:collision_box": {"width": 0.03, "height": 0.03}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:body_rotation_blocked": {}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 4, "knockback": true, "semi_random_diff_damage": false}}, "power": 1.2, "gravity": 0.0, "uncertainty_base": 0, "uncertainty_multiplier": 1, "anchor": 1, "offset": [0, -0.1, 0], "inertia": 0.98, "liquid_inertia": 1.0, "shoot_target": false}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:type_family": {"family": ["projectile", "inanimate", "necromancer"]}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_dropped_ticks": 0, "max_optimized_distance": 128, "use_motion_prediction_hints": true}}}}}