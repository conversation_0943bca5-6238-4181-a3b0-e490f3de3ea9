import { Entity } from "@minecraft/server";

/**
 * List of all sound effects for the Necromancer
 * Note: These are placeholders and should be updated when sound effects are added
 */
export const NECROMANCER_SOUNDS = [
  "mob.ptd_dbb_necromancer.death",
  "mob.ptd_dbb_necromancer.idle",
  "mob.ptd_dbb_necromancer.spawn",
  "mob.ptd_dbb_necromancer.move",
  "mob.ptd_dbb_necromancer.cataclysm",
  "mob.ptd_dbb_necromancer.soul_drain",
  "mob.ptd_dbb_necromancer.phantom_phase",
  "mob.ptd_dbb_necromancer.undead_summon",
  "mob.ptd_dbb_necromancer.arcane_blast",
  "mob.ptd_dbb_necromancer.soul_hands"
];

/**
 * Stops all Necromancer sound effects except the excluded one
 * Uses the stopsound command for more effective sound stopping
 *
 * @param entity The entity to stop sounds for
 * @param excludedSound Optional sound to exclude from stopping
 */
export function stopNecromancerSounds(entity: Entity, target?: Entity, excludedSound?: string): void {
  try {
    if (!entity) return;

    // Use the stopsound command to stop all sounds except the excluded one
    for (const sound of NECROMANCER_SOUNDS) {
      // Skip the excluded sound
      if (excludedSound && sound === excludedSound) continue;

      // Use the stopsound command to stop the sound
      try {
        entity.runCommand(`stopsound @a ${sound}`);

        if (target && (excludedSound === "soul_hands" || excludedSound === "soul_drain")) {
          target.dimension.playSound(`mob.ptd_dbb_necromancer.${excludedSound}`, target.location);
        }
      } catch (cmdError) {
        // If the command fails, log the error but continue with other sounds
        console.warn(`Error running stopsound command for ${sound}: ${cmdError}`);
      }
    }
  } catch (error) {
    console.warn(`Error stopping Necromancer sounds: ${error}`);
  }
}

/**
 * Maps attack names to their corresponding sound effects
 * Note: These are placeholders and should be updated when sound effects are added
 */
export const NECROMANCER_ATTACK_SOUND_MAP: Record<string, string> = {
  none: "",
  cataclysm: "mob.ptd_dbb_necromancer.cataclysm",
  soul_drain: "mob.ptd_dbb_necromancer.soul_drain",
  phantom_phase_start: "mob.ptd_dbb_necromancer.phantom_phase",
  phantom_phase_end: "mob.ptd_dbb_necromancer.phantom_phase",
  undead_summon: "mob.ptd_dbb_necromancer.undead_summon",
  arcane_blast: "mob.ptd_dbb_necromancer.arcane_blast",
  soul_hands: "mob.ptd_dbb_necromancer.soul_hands"
};
