{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.piglin_champion.general": {"states": {"default": {"animations": ["spawn"], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "idling": {"animations": ["idle"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"walking": "q.ground_speed > 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"horizontal_attack": "q.property('ptd_dbb:attack') == 'horizontal' && q.property('ptd_dbb:cooling_down') == false"}, {"vertical_attack": "q.property('ptd_dbb:attack') == 'vertical' && q.property('ptd_dbb:cooling_down') == false"}, {"foot_stomp": "q.property('ptd_dbb:attack') == 'foot_stomp' && q.property('ptd_dbb:cooling_down') == false"}, {"spin_slam": "q.property('ptd_dbb:attack') == 'spin_slam' && q.property('ptd_dbb:cooling_down') == false"}, {"body_slam": "q.property('ptd_dbb:attack') == 'body_slam' && q.property('ptd_dbb:cooling_down') == false"}, {"upchuck": "q.property('ptd_dbb:attack') == 'upchuck' && q.property('ptd_dbb:cooling_down') == false"}, {"charging": "q.property('ptd_dbb:attack') == 'charging' && q.property('ptd_dbb:cooling_down') == false"}, {"healing": "q.property('ptd_dbb:attack') == 'healing' && q.property('ptd_dbb:cooling_down') == false"}, {"summoning_chant": "q.property('ptd_dbb:attack') == 'summoning_chant' && q.property('ptd_dbb:cooling_down') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "walking": {"animations": ["walk"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.ground_speed < 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true && q.property('ptd_dbb:cooling_down') == false"}, {"horizontal_attack": "q.property('ptd_dbb:attack') == 'horizontal' && q.property('ptd_dbb:cooling_down') == false"}, {"vertical_attack": "q.property('ptd_dbb:attack') == 'vertical' && q.property('ptd_dbb:cooling_down') == false"}, {"foot_stomp": "q.property('ptd_dbb:attack') == 'foot_stomp' && q.property('ptd_dbb:cooling_down') == false"}, {"spin_slam": "q.property('ptd_dbb:attack') == 'spin_slam' && q.property('ptd_dbb:cooling_down') == false"}, {"body_slam": "q.property('ptd_dbb:attack') == 'body_slam' && q.property('ptd_dbb:cooling_down') == false"}, {"upchuck": "q.property('ptd_dbb:attack') == 'upchuck' && q.property('ptd_dbb:cooling_down') == false"}, {"charging": "q.property('ptd_dbb:attack') == 'charging' && q.property('ptd_dbb:cooling_down') == false"}, {"healing": "q.property('ptd_dbb:attack') == 'healing' && q.property('ptd_dbb:cooling_down') == false"}, {"summoning_chant": "q.property('ptd_dbb:attack') == 'summoning_chant' && q.property('ptd_dbb:cooling_down') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "horizontal_attack": {"animations": ["horizontal_attack"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "vertical_attack": {"animations": ["vertical_attack"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "foot_stomp": {"animations": ["foot_stomp"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "spin_slam": {"animations": ["spin_slam"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "body_slam": {"animations": ["body_slam"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "upchuck": {"animations": ["upchuck"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "charging": {"animations": ["charge_1"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_2": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "charging_2": {"animations": ["charge_2"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_3": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "charging_3": {"animations": ["charge_3"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_stunned_sitting": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.5, "blend_via_shortest_path": true}, "charging_stunned_sitting": {"animations": ["stunned_sitting"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_4": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.5, "blend_via_shortest_path": true}, "charging_4": {"animations": ["charge_4"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "healing": {"animations": ["healing"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"upchuck": "q.property('ptd_dbb:attack') == 'upchuck'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "summoning_chant": {"animations": ["summoning_chant"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "stunned": {"animations": ["stun"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "dead": {"animations": ["death"]}}}}}