import { Entity } from "@minecraft/server";

// Types for attack history
type AttackHistory = Record<string, number>;

// Available attacks for each range
export const SHORT_RANGE_ATTACKS = ["right_atomic"];

export const MEDIUM_RANGE_ATTACKS = ["right_atomic"];

export const LONG_RANGE_ATTACKS = ["none"];

export const UNREACHABLE_RANGE_ATTACKS = ["none"];

// Dynamic property names
const SHORT_RANGE_HISTORY_PROP = "shortRangeAttackHistory";
const MEDIUM_RANGE_HISTORY_PROP = "mediumRangeAttackHistory";
const LONG_RANGE_HISTORY_PROP = "longRangeAttackHistory";
const UNREACHABLE_RANGE_HISTORY_PROP = "unreachableRangeAttackHistory";

/**
 * Initialize attack history for a range
 * @param entity The entity to initialize attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @param attacks The available attacks for the range
 */
function initializeAttackHistory(entity: Entity, rangeName: string, attacks: string[]): void {
  const history: AttackHistory = {};
  for (const attack of attacks) {
    history[attack] = 0;
  }

  // Set the dynamic property
  const propName = getRangePropertyName(rangeName);
  entity.setDynamicProperty(propName, JSON.stringify(history));
}

/**
 * Get the dynamic property name for a range
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @returns The dynamic property name
 */
function getRangePropertyName(rangeName: string): string {
  switch (rangeName) {
    case "short":
      return SHORT_RANGE_HISTORY_PROP;
    case "medium":
      return MEDIUM_RANGE_HISTORY_PROP;
    case "long":
      return LONG_RANGE_HISTORY_PROP;
    case "unreachable":
      return UNREACHABLE_RANGE_HISTORY_PROP;
    default:
      return "";
  }
}

/**
 * Get available attacks for a range
 * @param entity The entity to get available attacks for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @param attacks The available attacks for the range
 * @returns The available attacks based on usage history
 */
export function getAvailableAttacks(entity: Entity, rangeName: string, attacks: string[]): string[] {
  const propName = getRangePropertyName(rangeName);
  const historyJson = entity.getDynamicProperty(propName) as string;

  // Initialize attack history if it doesn't exist
  if (!historyJson) {
    initializeAttackHistory(entity, rangeName, attacks);
    return [...attacks]; // Return a copy of the attacks array
  }

  const history: AttackHistory = JSON.parse(historyJson);

  // Check if all attacks have been used twice
  const allUsedTwice = attacks.every((attack) => (history[attack] ?? 0) >= 2);
  if (allUsedTwice) {
    // Reset history if all attacks have been used twice
    initializeAttackHistory(entity, rangeName, attacks);
    return [...attacks]; // Return a copy of the attacks array
  }

  // Check if any attack hasn't been used yet
  const unusedAttacks = attacks.filter((attack) => (history[attack] ?? 0) === 0);
  if (unusedAttacks.length > 0) {
    return unusedAttacks;
  }

  // If all attacks have been used at least once, return attacks used less than twice
  return attacks.filter((attack) => (history[attack] ?? 0) < 2);
}

/**
 * Update attack history after an attack is selected
 * @param entity The entity to update attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @param attack The selected attack
 */
export function updateAttackHistory(entity: Entity, rangeName: string, attack: string): void {
  const propName = getRangePropertyName(rangeName);
  const historyJson = entity.getDynamicProperty(propName) as string;
  if (!historyJson) {
    return;
  }

  const history: AttackHistory = JSON.parse(historyJson);
  history[attack] = (history[attack] ?? 0) + 1;

  entity.setDynamicProperty(propName, JSON.stringify(history));
}

/**
 * Get the attack history for a range
 * @param entity The entity to get attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @returns The attack history as a Record<string, number> or null if not found
 */
export function getAttackHistory(entity: Entity, rangeName: string): AttackHistory | null {
  const propName = getRangePropertyName(rangeName);
  const historyJson = entity.getDynamicProperty(propName) as string;

  if (!historyJson) {
    return null;
  }

  return JSON.parse(historyJson) as AttackHistory;
}

/**
 * Display attack history on the actionbar
 * @param entity The entity to get attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 */
export function displayAttackHistory(entity: Entity, rangeName: string): void {
  const history = getAttackHistory(entity, rangeName);
  if (!history) {
    return;
  }
  // For now, we don't display the history, but this function can be expanded in the future
}
