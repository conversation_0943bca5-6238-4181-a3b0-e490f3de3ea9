import { defaultEventWeights } from "./constants";
import { dimensions } from "../../main";
/**
 * Map to store attack weights for each Grimhowl boss instance
 * Key: Entity ID, Value: Attack weights object
 */
const entityEventWeights = new Map();
/**
 * Tracks the last selected attack to prevent repetition
 */
let lastSelectedEvent = null;
/**
 * Gets the attack weights for a specific Grimhowl boss instance
 * Adjusts backstep weight based on number of arrows stuck in the boss
 *
 * @param entityId - The ID of the Grimhowl boss entity
 * @returns The attack weights for the entity
 */
export function getEntityWeights(entityId) {
    let entity;
    for (const dim of dimensions) {
        entity = dim.getEntities({}).find((e) => e.id === entityId);
        if (entity)
            break;
    }
    let backstepWeight = defaultEventWeights.backstep;
    if (entity && entity.typeId === "ptd_dbb:grimhowl") {
        // Every arrow stuck on Grimhowl would reduce the backstep weight by 10/12
        let arrowHits = 0;
        for (let i = 1; i <= 12; i++) {
            if (entity.getProperty(`ptd_dbb:arrow_hit_${i}`))
                arrowHits++;
        }
        backstepWeight = defaultEventWeights.backstep - (10 / 12) * arrowHits;
    }
    if (!entityEventWeights.has(entityId)) {
        entityEventWeights.set(entityId, { ...defaultEventWeights, backstep: backstepWeight });
    }
    else {
        const weights = entityEventWeights.get(entityId);
        weights.backstep = backstepWeight;
    }
    return entityEventWeights.get(entityId);
}
/**
 * Normalizes attack weights to ensure they sum to 1
 * This converts raw weights to probabilities
 *
 * @param weights - The raw attack weights
 * @returns Normalized weights that sum to 1
 */
export function normalizeWeights(weights) {
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    return Object.fromEntries(Object.entries(weights).map(([key, weight]) => [key, weight / totalWeight]));
}
/**
 * Selects an attack based on weighted probabilities
 *
 * @param weights - The attack weights
 * @returns The selected attack name
 */
export function selectEvent(weights) {
    const normalizedWeights = normalizeWeights(weights);
    const random = Math.random();
    let cumulative = 0;
    for (const [event, weight] of Object.entries(normalizedWeights)) {
        cumulative += weight;
        if (random < cumulative) {
            return event;
        }
    }
    return Object.keys(normalizedWeights)[0];
}
/**
 * Updates attack weights after an attack is selected
 * Reduces the weight of the selected attack and increases others
 * to encourage varied attack patterns
 *
 * @param entityId - The ID of the Grimhowl boss entity
 * @param lastEvent - The last selected attack
 */
export function updateWeights(entityId, lastEvent) {
    let weights = getEntityWeights(entityId);
    if (lastSelectedEvent !== lastEvent) {
        weights = { ...defaultEventWeights };
    }
    Object.keys(weights).forEach(event => {
        weights[event] += event === lastEvent ? -0.5 : 0.2;
        weights[event] = Math.max(weights[event], 0.1);
    });
    entityEventWeights.set(entityId, weights);
    lastSelectedEvent = lastEvent;
}
/**
 * Resets attack weights for a specific entity or all entities
 *
 * @param entityId - Optional ID of the entity to reset weights for
 *                   If not provided, resets weights for all entities
 */
export function resetEntityWeights(entityId) {
    if (entityId) {
        entityEventWeights.delete(entityId);
    }
    else {
        entityEventWeights.clear();
    }
    lastSelectedEvent = null;
}
