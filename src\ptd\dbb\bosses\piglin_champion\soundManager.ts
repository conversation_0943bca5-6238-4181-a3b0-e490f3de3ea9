import { Entity } from "@minecraft/server";

/**
 * List of all sound effects for the Piglin Champion
 */
export const PIGLIN_CHAMPION_SOUNDS = [
    "mob.ptd_dbb_piglin_champion.body_slam",
    "mob.ptd_dbb_piglin_champion.damaged_to_stunned",
    "mob.ptd_dbb_piglin_champion.death",
    "mob.ptd_dbb_piglin_champion.foot_stomp",
    "mob.ptd_dbb_piglin_champion.healing",
    "mob.ptd_dbb_piglin_champion.horizontal_attack",
    "mob.ptd_dbb_piglin_champion.idle",
    "mob.ptd_dbb_piglin_champion.spawn",
    "mob.ptd_dbb_piglin_champion.spin_slam",
    "mob.ptd_dbb_piglin_champion.stunned_standing",
    "mob.ptd_dbb_piglin_champion.stunned_to_idle",
    "mob.ptd_dbb_piglin_champion.summoning_chant",
    "mob.ptd_dbb_piglin_champion.upchuck",
    "mob.ptd_dbb_piglin_champion.vertical_attack",
    "mob.ptd_dbb_piglin_champion.walk"
];

/**
 * Stops all Piglin Champion sound effects except the excluded one
 * Uses the stopsound command for more effective sound stopping
 *
 * @param entity The entity to stop sounds for
 * @param excludedSound Optional sound to exclude from stopping
 */
export function stopPiglinChampionSounds(entity: Entity, excludedSound?: string): void | undefined {
    try {
        if (!entity) return;

        // Use the stopsound command to stop all sounds except the excluded one
        for (const sound of PIGLIN_CHAMPION_SOUNDS) {
            // Skip the excluded sound
            if (excludedSound && sound === excludedSound) continue;

            // Use the stopsound command to stop the sound
            try {
                entity.runCommand(`stopsound @a ${sound}`);
            } catch (cmdError) {
                // If the command fails, log the error but continue with other sounds
                console.warn(`Error running stopsound command for ${sound}: ${cmdError}`);
            }
        }
    } catch (error) {
        console.warn(`Error stopping Piglin Champion sounds: ${error}`);
    }
}

/**
 * Maps attack names to their corresponding sound effects
 */
export const ATTACK_SOUND_MAP: Record<string, string> = {
    "horizontal": "mob.ptd_dbb_piglin_champion.horizontal_attack",
    "vertical": "mob.ptd_dbb_piglin_champion.vertical_attack",
    "foot_stomp": "mob.ptd_dbb_piglin_champion.foot_stomp",
    "spin_slam": "mob.ptd_dbb_piglin_champion.spin_slam",
    "body_slam": "mob.ptd_dbb_piglin_champion.body_slam",
    "charging": "mob.ptd_dbb_piglin_champion.charging",
    "summoning_chant": "mob.ptd_dbb_piglin_champion.summoning_chant",
    "upchuck": "mob.ptd_dbb_piglin_champion.upchuck",
    "healing": "mob.ptd_dbb_piglin_champion.healing",
    "stunned_standing": "mob.ptd_dbb_piglin_champion.stunned_standing",
    "stunned_sitting": "mob.ptd_dbb_piglin_champion.stunned_standing", // Reuse standing sound
    "none": ""
};
