{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.piglin_marauder.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": [{"idle": "v.random_idle == 0"}, {"idle_2": "v.random_idle == 1"}, {"idle_3": "v.random_idle == 2"}], "on_entry": ["v.random_idle = math.random_integer(0, 2);"], "transitions": [{"moving": "q.ground_speed > 0.3"}, {"running": "q.ground_speed > 0.3 && q.has_target"}, {"slam_attack": "q.property('ptd_dbb:attack') == 'slam'"}, {"sweep_attack": "q.property('ptd_dbb:attack') == 'sweep'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "moving": {"animations": ["walk"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"running": "q.ground_speed > 0.3 && q.has_target"}, {"slam_attack": "q.property('ptd_dbb:attack') == 'slam'"}, {"sweep_attack": "q.property('ptd_dbb:attack') == 'sweep'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "running": {"animations": ["run"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"slam_attack": "q.property('ptd_dbb:attack') == 'slam'"}, {"sweep_attack": "q.property('ptd_dbb:attack') == 'sweep'"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"moving": "q.ground_speed > 0.3 && !q.has_target"}], "blend_transition": 0.3}, "slam_attack": {"animations": ["slam_attack"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "sweep_attack": {"animations": ["sweep_attack"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}