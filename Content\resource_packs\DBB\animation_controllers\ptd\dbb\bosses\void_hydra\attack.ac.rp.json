{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.void_hydra.attack": {"states": {"default": {"transitions": [{"right_atomic_cross": "q.property('ptd_dbb:attack') == 'right_atomic_cross' && q.property('ptd_dbb:cooling_down') == false"}, {"right_atomic": "q.property('ptd_dbb:attack') == 'right_atomic' && q.property('ptd_dbb:cooling_down') == false"}, {"right_vacuum": "q.property('ptd_dbb:attack') == 'right_vacuum' && q.property('ptd_dbb:cooling_down') == false"}, {"right_summon": "q.property('ptd_dbb:attack') == 'right_summon' && q.property('ptd_dbb:cooling_down') == false"}, {"mid_atomic": "q.property('ptd_dbb:attack') == 'mid_atomic' && q.property('ptd_dbb:cooling_down') == false"}, {"mid_meteor": "q.property('ptd_dbb:attack') == 'mid_meteor' && q.property('ptd_dbb:cooling_down') == false"}, {"mid_singularity": "q.property('ptd_dbb:attack') == 'mid_singularity' && q.property('ptd_dbb:cooling_down') == false"}, {"left_atomic_cross": "q.property('ptd_dbb:attack') == 'left_atomic_cross' && q.property('ptd_dbb:cooling_down') == false"}, {"left_atomic": "q.property('ptd_dbb:attack') == 'left_atomic' && q.property('ptd_dbb:cooling_down') == false"}, {"left_railgun": "q.property('ptd_dbb:attack') == 'left_railgun' && q.property('ptd_dbb:cooling_down') == false"}, {"left_missile": "q.property('ptd_dbb:attack') == 'left_missile' && q.property('ptd_dbb:cooling_down') == false"}, {"left_shout": "q.property('ptd_dbb:attack') == 'left_shout' && q.property('ptd_dbb:cooling_down') == false"}, {"dead": "q.property('ptd_dbb:dead') == true && q.property('ptd_dbb:cooling_down') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "right_atomic_cross": {"animations": ["right_atomic_cross"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "right_atomic": {"animations": ["right_atomic"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "right_vacuum": {"animations": ["right_vacuum"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "right_summon": {"animations": ["right_summon"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "mid_atomic": {"animations": ["mid_atomic"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "mid_meteor": {"animations": ["mid_meteor"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "mid_singularity": {"animations": ["mid_singularity"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "left_atomic_cross": {"animations": ["left_atomic_cross"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "left_atomic": {"animations": ["left_atomic"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "left_railgun": {"animations": ["left_railgun"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "left_missile": {"animations": ["left_missile"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "left_shout": {"animations": ["left_shout"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "dead": {}}}}}