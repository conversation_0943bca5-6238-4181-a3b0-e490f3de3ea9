{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.skeleton_soul.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": ["idle"], "transitions": [{"moving": "q.ground_speed > 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "moving": {"animations": ["walk"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}, "controller.animation.ptd_dbb.skeleton_soul.attack": {"states": {"default": {"transitions": [{"attacking": "q.is_delayed_attacking"}], "blend_transition": 0.1}, "attacking": {"animations": ["attack"], "transitions": [{"default": "!q.is_delayed_attacking || q.all_animations_finished"}], "blend_transition": 0.1}}}}}