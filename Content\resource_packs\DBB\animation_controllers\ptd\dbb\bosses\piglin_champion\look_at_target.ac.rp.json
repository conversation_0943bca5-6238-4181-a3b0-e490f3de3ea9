{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.piglin_champion.look_at_target": {"initial_state": "not_looking", "states": {"not_looking": {"transitions": [{"looking": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && q.property('ptd_dbb:attack') != 'charging' && q.property('ptd_dbb:attack') != 'stunned_standing' && q.property('ptd_dbb:attack') != 'stunned_sitting'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "looking": {"animations": ["look_at_target"], "transitions": [{"not_looking": "q.property('ptd_dbb:spawning') == true || q.property('ptd_dbb:dead') == true || q.property('ptd_dbb:attack') == 'charging' || q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}