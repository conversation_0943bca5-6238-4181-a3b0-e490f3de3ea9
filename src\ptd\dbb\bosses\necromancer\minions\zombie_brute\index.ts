import { Entity } from "@minecraft/server";
import { executeSplashAttack } from "./attacks/splash";

/**
 * The total animation time in ticks / length of the slowness effect
 */
const ANIMATION_TIME = 88; // Total animation time in ticks

/**
 * Handles all mechanics for the Zombie Brute
 * This includes attack selection, attack execution, and cooldown management
 *
 * @param zombieBrute The zombie brute entity
 */
export function zombieBruteMechanics(zombieBrute: Entity): void {
  // Skip if entity is not valid
  try {
    if (!zombieBrute) return;

    // Skip if entity is spawning or dead
    const isSpawning = zombieBrute.getProperty("ptd_dbb:spawning") as boolean;
    const isDead = zombieBrute.getProperty("ptd_dbb:dead") as boolean;
    if (isSpawning || isDead) return;

    // Apply slowness effect to prevent movement during the attack
    zombieBrute.addEffect("minecraft:slowness", ANIMATION_TIME, { amplifier: 250, showParticles: false });

    executeSplashAttack(zombieBrute);
  } catch (e) {
    return;
  }
  return;
}
